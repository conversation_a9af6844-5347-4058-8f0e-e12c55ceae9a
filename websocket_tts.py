"""
WebSocket TTS 服务器
基于 GPT-SoVITS 的原生 WebSocket 实时语音合成服务

使用方法:
python websocket_tts.py -c GPT_SoVITS/configs/tts_infer.yaml -p 9881

WebSocket 连接地址: ws://localhost:9881/ws

消息格式:
{
    "type": "tts_request",
    "data": {
        "text": "要合成的文本",
        "text_lang": "zh",
        "ref_audio_path": "参考音频路径",
        "prompt_text": "参考音频文本",
        "prompt_lang": "zh",
        "streaming": true,
        "media_type": "wav"
    }
}

响应格式:
{
    "type": "audio_chunk",
    "data": {
        "audio": "base64编码的音频数据",
        "sample_rate": 24000,
        "is_final": false
    }
}
"""

import argparse
import asyncio
import base64
import json
import logging
import os
import sys
import traceback
from typing import Dict, Any, Optional
import uuid

import numpy as np
import soundfile as sf
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
import uvicorn
from io import BytesIO

# 添加项目路径
now_dir = os.getcwd()
sys.path.append(now_dir)
sys.path.append("%s/GPT_SoVITS" % (now_dir))

from GPT_SoVITS.TTS_infer_pack.TTS import TTS, TTS_Config
from tools.i18n.i18n import I18nAuto

# 初始化国际化
i18n = I18nAuto()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WebSocketTTSServer:
    def __init__(self, config_path: str, host: str = "0.0.0.0", port: int = 9881):
        self.config_path = config_path
        self.host = host
        self.port = port
        self.app = FastAPI(title="WebSocket TTS Server")
        
        # 初始化TTS引擎
        self.tts_config = TTS_Config(config_path)
        self.tts_pipeline = TTS(self.tts_config)
        
        # 连接管理
        self.active_connections: Dict[str, WebSocket] = {}
        
        # 设置路由
        self.setup_routes()
        
        logger.info(f"WebSocket TTS服务器初始化完成")
        logger.info(f"配置文件: {config_path}")
        logger.info(f"模型版本: {self.tts_config.version}")
        logger.info(f"设备: {self.tts_config.device}")

    def setup_routes(self):
        """设置路由"""
        
        @self.app.get("/")
        async def get_index():
            """返回测试页面"""
            return HTMLResponse(content=self.get_test_page())
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket连接端点"""
            await self.handle_websocket_connection(websocket)
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy",
                "version": self.tts_config.version,
                "device": str(self.tts_config.device)
            }

    async def handle_websocket_connection(self, websocket: WebSocket):
        """处理WebSocket连接"""
        connection_id = str(uuid.uuid4())
        
        try:
            await websocket.accept()
            self.active_connections[connection_id] = websocket
            logger.info(f"WebSocket连接建立: {connection_id}")
            
            # 发送连接确认消息
            await websocket.send_json({
                "type": "connection_established",
                "data": {
                    "connection_id": connection_id,
                    "server_info": {
                        "version": self.tts_config.version,
                        "device": str(self.tts_config.device),
                        "supported_languages": ["zh", "en", "ja", "ko", "yue"]
                    }
                }
            })
            
            # 处理消息循环
            while True:
                try:
                    # 接收消息
                    message = await websocket.receive_json()
                    await self.process_message(websocket, connection_id, message)
                    
                except WebSocketDisconnect:
                    logger.info(f"WebSocket连接断开: {connection_id}")
                    break
                except Exception as e:
                    logger.error(f"处理消息时出错: {e}")
                    await self.send_error(websocket, str(e))
                    
        except Exception as e:
            logger.error(f"WebSocket连接处理出错: {e}")
        finally:
            # 清理连接
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
            logger.info(f"WebSocket连接清理完成: {connection_id}")

    async def process_message(self, websocket: WebSocket, connection_id: str, message: Dict[str, Any]):
        """处理接收到的消息"""
        message_type = message.get("type")
        
        if message_type == "tts_request":
            await self.handle_tts_request(websocket, connection_id, message.get("data", {}))
        elif message_type == "ping":
            await websocket.send_json({"type": "pong", "data": {"timestamp": message.get("data", {}).get("timestamp")}})
        elif message_type == "stop_tts":
            # 停止当前TTS任务
            self.tts_pipeline.stop()
            await websocket.send_json({"type": "tts_stopped", "data": {}})
        else:
            await self.send_error(websocket, f"未知的消息类型: {message_type}")

    async def handle_tts_request(self, websocket: WebSocket, connection_id: str, request_data: Dict[str, Any]):
        """处理TTS请求"""
        try:
            # 验证必需参数
            text = request_data.get("text", "").strip()
            if not text:
                await self.send_error(websocket, "文本不能为空")
                return

            text_lang = request_data.get("text_lang", "zh").lower()
            ref_audio_path = request_data.get("ref_audio_path", "")
            prompt_text = request_data.get("prompt_text", "")
            prompt_lang = request_data.get("prompt_lang", "zh").lower()
            streaming = request_data.get("streaming", True)
            media_type = request_data.get("media_type", "wav")

            # 构建TTS参数
            tts_params = {
                "text": text,
                "text_lang": text_lang,
                "ref_audio_path": ref_audio_path,
                "prompt_text": prompt_text,
                "prompt_lang": prompt_lang,
                "return_fragment": streaming,  # 启用分段返回
                "top_k": request_data.get("top_k", 5),
                "top_p": request_data.get("top_p", 1.0),
                "temperature": request_data.get("temperature", 1.0),
                "text_split_method": request_data.get("text_split_method", "cut5"),
                "batch_size": request_data.get("batch_size", 1),
                "speed_factor": request_data.get("speed_factor", 1.0),
                "fragment_interval": request_data.get("fragment_interval", 0.3),
                "seed": request_data.get("seed", -1),
                "parallel_infer": request_data.get("parallel_infer", True),
                "repetition_penalty": request_data.get("repetition_penalty", 1.35),
                "sample_steps": request_data.get("sample_steps", 32),
                "super_sampling": request_data.get("super_sampling", False)
            }

            logger.info(f"开始TTS合成: {connection_id}, 文本长度: {len(text)}")

            # 发送开始消息
            await websocket.send_json({
                "type": "tts_started",
                "data": {
                    "text": text,
                    "text_lang": text_lang,
                    "streaming": streaming
                }
            })

            # 执行TTS合成
            if streaming:
                await self.stream_tts_audio(websocket, connection_id, tts_params, media_type)
            else:
                await self.generate_complete_audio(websocket, connection_id, tts_params, media_type)

        except Exception as e:
            logger.error(f"TTS请求处理失败: {e}")
            traceback.print_exc()
            await self.send_error(websocket, f"TTS合成失败: {str(e)}")

    async def stream_tts_audio(self, websocket: WebSocket, connection_id: str, tts_params: Dict[str, Any], media_type: str):
        """流式TTS音频生成"""
        try:
            # 使用TTS生成器
            tts_generator = self.tts_pipeline.run(tts_params)

            chunk_count = 0
            for sample_rate, audio_chunk in tts_generator:
                # 检查连接是否仍然活跃
                if connection_id not in self.active_connections:
                    logger.info(f"连接已断开，停止TTS生成: {connection_id}")
                    break

                # 将音频转换为指定格式
                audio_data = self.encode_audio(audio_chunk, sample_rate, media_type)

                chunk_count += 1
                is_final = False  # 在流式模式下，我们不知道何时结束，所以设为False

                # 发送音频片段
                await websocket.send_json({
                    "type": "audio_chunk",
                    "data": {
                        "audio": audio_data,
                        "sample_rate": sample_rate,
                        "chunk_index": chunk_count,
                        "is_final": is_final,
                        "media_type": media_type
                    }
                })

                logger.debug(f"发送音频片段 {chunk_count}: {connection_id}")

            # 发送完成消息
            await websocket.send_json({
                "type": "audio_chunk",
                "data": {
                    "audio": "",
                    "sample_rate": sample_rate if 'sample_rate' in locals() else 24000,
                    "chunk_index": chunk_count + 1,
                    "is_final": True,
                    "media_type": media_type
                }
            })

            logger.info(f"TTS流式合成完成: {connection_id}, 共 {chunk_count} 个片段")

        except Exception as e:
            logger.error(f"流式TTS生成失败: {e}")
            await self.send_error(websocket, f"流式TTS生成失败: {str(e)}")

    async def generate_complete_audio(self, websocket: WebSocket, connection_id: str, tts_params: Dict[str, Any], media_type: str):
        """生成完整音频"""
        try:
            # 禁用分段返回
            tts_params["return_fragment"] = False

            # 使用TTS生成器
            tts_generator = self.tts_pipeline.run(tts_params)

            # 获取完整音频
            sample_rate, complete_audio = next(tts_generator)

            # 将音频转换为指定格式
            audio_data = self.encode_audio(complete_audio, sample_rate, media_type)

            # 发送完整音频
            await websocket.send_json({
                "type": "audio_chunk",
                "data": {
                    "audio": audio_data,
                    "sample_rate": sample_rate,
                    "chunk_index": 1,
                    "is_final": True,
                    "media_type": media_type
                }
            })

            logger.info(f"TTS完整合成完成: {connection_id}")

        except Exception as e:
            logger.error(f"完整TTS生成失败: {e}")
            await self.send_error(websocket, f"完整TTS生成失败: {str(e)}")

    def encode_audio(self, audio_data: np.ndarray, sample_rate: int, media_type: str) -> str:
        """将音频数据编码为base64字符串"""
        try:
            # 创建内存缓冲区
            buffer = BytesIO()

            # 根据媒体类型保存音频
            if media_type.lower() == "wav":
                sf.write(buffer, audio_data, sample_rate, format='WAV')
            elif media_type.lower() == "ogg":
                sf.write(buffer, audio_data, sample_rate, format='OGG')
            elif media_type.lower() == "flac":
                sf.write(buffer, audio_data, sample_rate, format='FLAC')
            else:
                # 默认使用WAV格式
                sf.write(buffer, audio_data, sample_rate, format='WAV')

            # 获取音频字节数据
            buffer.seek(0)
            audio_bytes = buffer.read()

            # 编码为base64
            return base64.b64encode(audio_bytes).decode('utf-8')

        except Exception as e:
            logger.error(f"音频编码失败: {e}")
            raise

    async def send_error(self, websocket: WebSocket, error_message: str):
        """发送错误消息"""
        try:
            await websocket.send_json({
                "type": "error",
                "data": {"message": error_message}
            })
        except Exception as e:
            logger.error(f"发送错误消息失败: {e}")

    def get_test_page(self) -> str:
        """获取测试页面HTML"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>WebSocket TTS 测试页面</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 800px; margin: 0 auto; }
                .form-group { margin-bottom: 15px; }
                label { display: block; margin-bottom: 5px; font-weight: bold; }
                input, textarea, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
                button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
                button:hover { background: #0056b3; }
                button:disabled { background: #ccc; cursor: not-allowed; }
                .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
                .status.connected { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
                .status.disconnected { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
                .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
                audio { width: 100%; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>WebSocket TTS 测试页面</h1>
                
                <div id="status" class="status disconnected">未连接</div>
                
                <div class="form-group">
                    <label>WebSocket地址:</label>
                    <input type="text" id="wsUrl" value="ws://localhost:9881/ws">
                    <button onclick="connect()" id="connectBtn">连接</button>
                    <button onclick="disconnect()" id="disconnectBtn" disabled>断开</button>
                </div>
                
                <div class="form-group">
                    <label>要合成的文本:</label>
                    <textarea id="text" rows="3" placeholder="请输入要合成的文本...">你好，这是一个WebSocket TTS测试。</textarea>
                </div>
                
                <div class="form-group">
                    <label>文本语言:</label>
                    <select id="textLang">
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                        <option value="yue">粤语</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>参考音频路径:</label>
                    <input type="text" id="refAudioPath" placeholder="参考音频文件路径">
                </div>
                
                <div class="form-group">
                    <label>参考音频文本:</label>
                    <input type="text" id="promptText" placeholder="参考音频对应的文本">
                </div>
                
                <div class="form-group">
                    <label>参考音频语言:</label>
                    <select id="promptLang">
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                        <option value="yue">粤语</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <button onclick="sendTTSRequest()" id="ttsBtn" disabled>开始语音合成</button>
                    <button onclick="stopTTS()" id="stopBtn" disabled>停止合成</button>
                </div>
                
                <div class="form-group">
                    <label>生成的音频:</label>
                    <audio id="audioPlayer" controls></audio>
                </div>
                
                <div class="form-group">
                    <label>日志:</label>
                    <div id="log" class="log"></div>
                </div>
            </div>
            
            <script>
                let ws = null;
                let audioChunks = [];
                
                function log(message) {
                    const logDiv = document.getElementById('log');
                    const timestamp = new Date().toLocaleTimeString();
                    logDiv.innerHTML += `[${timestamp}] ${message}\\n`;
                    logDiv.scrollTop = logDiv.scrollHeight;
                }
                
                function updateStatus(connected) {
                    const statusDiv = document.getElementById('status');
                    const connectBtn = document.getElementById('connectBtn');
                    const disconnectBtn = document.getElementById('disconnectBtn');
                    const ttsBtn = document.getElementById('ttsBtn');
                    
                    if (connected) {
                        statusDiv.textContent = '已连接';
                        statusDiv.className = 'status connected';
                        connectBtn.disabled = true;
                        disconnectBtn.disabled = false;
                        ttsBtn.disabled = false;
                    } else {
                        statusDiv.textContent = '未连接';
                        statusDiv.className = 'status disconnected';
                        connectBtn.disabled = false;
                        disconnectBtn.disabled = true;
                        ttsBtn.disabled = true;
                        document.getElementById('stopBtn').disabled = true;
                    }
                }
                
                function connect() {
                    const url = document.getElementById('wsUrl').value;
                    ws = new WebSocket(url);
                    
                    ws.onopen = function() {
                        log('WebSocket连接已建立');
                        updateStatus(true);
                    };
                    
                    ws.onmessage = function(event) {
                        const message = JSON.parse(event.data);
                        handleMessage(message);
                    };
                    
                    ws.onclose = function() {
                        log('WebSocket连接已关闭');
                        updateStatus(false);
                        ws = null;
                    };
                    
                    ws.onerror = function(error) {
                        log('WebSocket错误: ' + error);
                        updateStatus(false);
                    };
                }
                
                function disconnect() {
                    if (ws) {
                        ws.close();
                    }
                }
                
                function handleMessage(message) {
                    log(`收到消息: ${message.type}`);
                    
                    switch (message.type) {
                        case 'connection_established':
                            log(`连接ID: ${message.data.connection_id}`);
                            log(`服务器版本: ${message.data.server_info.version}`);
                            log(`设备: ${message.data.server_info.device}`);
                            break;
                            
                        case 'tts_started':
                            log('TTS合成开始');
                            audioChunks = [];
                            document.getElementById('stopBtn').disabled = false;
                            break;
                            
                        case 'audio_chunk':
                            const audioData = message.data.audio;
                            audioChunks.push(audioData);
                            log(`收到音频片段 (${audioChunks.length})`);
                            
                            if (message.data.is_final) {
                                log('音频合成完成，正在播放...');
                                playAudio();
                                document.getElementById('stopBtn').disabled = true;
                            }
                            break;
                            
                        case 'tts_stopped':
                            log('TTS合成已停止');
                            document.getElementById('stopBtn').disabled = true;
                            break;
                            
                        case 'error':
                            log(`错误: ${message.data.message}`);
                            document.getElementById('stopBtn').disabled = true;
                            break;
                            
                        default:
                            log(`未知消息类型: ${message.type}`);
                    }
                }
                
                function sendTTSRequest() {
                    if (!ws) return;
                    
                    const request = {
                        type: 'tts_request',
                        data: {
                            text: document.getElementById('text').value,
                            text_lang: document.getElementById('textLang').value,
                            ref_audio_path: document.getElementById('refAudioPath').value,
                            prompt_text: document.getElementById('promptText').value,
                            prompt_lang: document.getElementById('promptLang').value,
                            streaming: true,
                            media_type: 'wav'
                        }
                    };
                    
                    ws.send(JSON.stringify(request));
                    log('发送TTS请求');
                }
                
                function stopTTS() {
                    if (!ws) return;
                    
                    ws.send(JSON.stringify({
                        type: 'stop_tts',
                        data: {}
                    }));
                    log('发送停止TTS请求');
                }
                
                function playAudio() {
                    if (audioChunks.length === 0) return;
                    
                    // 将所有音频片段合并
                    const combinedAudio = audioChunks.join('');
                    const audioBlob = base64ToBlob(combinedAudio, 'audio/wav');
                    const audioUrl = URL.createObjectURL(audioBlob);
                    
                    const audioPlayer = document.getElementById('audioPlayer');
                    audioPlayer.src = audioUrl;
                    audioPlayer.play();
                }
                
                function base64ToBlob(base64, mimeType) {
                    const byteCharacters = atob(base64);
                    const byteNumbers = new Array(byteCharacters.length);
                    for (let i = 0; i < byteCharacters.length; i++) {
                        byteNumbers[i] = byteCharacters.charCodeAt(i);
                    }
                    const byteArray = new Uint8Array(byteNumbers);
                    return new Blob([byteArray], {type: mimeType});
                }
            </script>
        </body>
        </html>
        """

    def run(self):
        """启动服务器"""
        logger.info(f"启动WebSocket TTS服务器: {self.host}:{self.port}")
        logger.info(f"测试页面: http://{self.host}:{self.port}")
        logger.info(f"WebSocket地址: ws://{self.host}:{self.port}/ws")
        uvicorn.run(self.app, host=self.host, port=self.port, log_level="info")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="WebSocket TTS 服务器")
    parser.add_argument(
        "-c", "--config",
        type=str,
        default="GPT_SoVITS/configs/tts_infer.yaml",
        help="TTS配置文件路径"
    )
    parser.add_argument(
        "-a", "--host",
        type=str,
        default="0.0.0.0",
        help="绑定地址"
    )
    parser.add_argument(
        "-p", "--port",
        type=int,
        default=9881,
        help="绑定端口"
    )

    args = parser.parse_args()

    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        logger.error(f"配置文件不存在: {args.config}")
        sys.exit(1)

    try:
        # 创建并启动服务器
        server = WebSocketTTSServer(
            config_path=args.config,
            host=args.host,
            port=args.port
        )
        server.run()

    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
