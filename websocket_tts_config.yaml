# WebSocket TTS 服务配置文件
# 基于 GPT-SoVITS 的 WebSocket 实时语音合成服务配置

# 服务器配置
server:
  host: "0.0.0.0"          # 绑定地址，0.0.0.0 表示监听所有接口
  port: 9881               # 服务端口
  log_level: "info"        # 日志级别: debug, info, warning, error

# TTS 模型配置 (继承自 GPT_SoVITS/configs/tts_infer.yaml)
tts:
  config_path: "GPT_SoVITS/configs/tts_infer.yaml"  # TTS 配置文件路径
  
  # 默认参数 (可被客户端请求覆盖)
  default_params:
    top_k: 5
    top_p: 1.0
    temperature: 1.0
    text_split_method: "cut5"
    batch_size: 1
    speed_factor: 1.0
    fragment_interval: 0.3
    seed: -1
    parallel_infer: true
    repetition_penalty: 1.35
    sample_steps: 32
    super_sampling: false

# WebSocket 配置
websocket:
  # 连接超时时间 (秒)
  connection_timeout: 60
  
  # 最大连接数
  max_connections: 100
  
  # 消息大小限制 (字节)
  max_message_size: 1048576  # 1MB
  
  # 心跳间隔 (秒)
  ping_interval: 30
  
  # 音频编码格式支持
  supported_audio_formats:
    - "wav"
    - "ogg" 
    - "flac"

# 安全配置
security:
  # 允许的来源 (CORS)
  allowed_origins:
    - "*"  # 开发环境，生产环境应该限制具体域名
  
  # 允许的文本长度限制
  max_text_length: 1000
  
  # 速率限制 (每分钟请求数)
  rate_limit: 60

# 音频处理配置
audio:
  # 默认采样率
  default_sample_rate: 24000
  
  # 音频质量设置
  quality:
    wav:
      bit_depth: 16
      format: "PCM_16"
    ogg:
      quality: 5  # 0-10, 10为最高质量
    flac:
      compression_level: 5  # 0-8, 8为最高压缩

# 缓存配置
cache:
  # 是否启用参考音频缓存
  enable_ref_audio_cache: true
  
  # 缓存大小限制 (MB)
  max_cache_size: 512
  
  # 缓存过期时间 (秒)
  cache_expire_time: 3600

# 监控和日志
monitoring:
  # 是否启用性能监控
  enable_metrics: true
  
  # 日志文件路径
  log_file: "logs/websocket_tts.log"
  
  # 是否记录音频生成统计
  log_audio_stats: true

# 开发配置
development:
  # 是否启用调试模式
  debug: false
  
  # 是否自动重载
  auto_reload: false
  
  # 测试页面配置
  test_page:
    enabled: true
    title: "WebSocket TTS 测试页面"
