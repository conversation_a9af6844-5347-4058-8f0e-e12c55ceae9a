#!/usr/bin/env python3
"""
WebSocket TTS 客户端示例
演示如何使用Python连接WebSocket TTS服务
"""

import asyncio
import base64
import json
import logging
import argparse
from pathlib import Path
import websockets
import soundfile as sf
from io import BytesIO

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WebSocketTTSClient:
    def __init__(self, url: str):
        self.url = url
        self.websocket = None
        self.audio_chunks = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(self.url)
            logger.info(f"已连接到: {self.url}")
            return True
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            logger.info("连接已断开")
    
    async def send_message(self, message: dict):
        """发送消息"""
        if not self.websocket:
            raise Exception("未连接到服务器")
        
        await self.websocket.send(json.dumps(message))
        logger.debug(f"发送消息: {message['type']}")
    
    async def receive_message(self):
        """接收消息"""
        if not self.websocket:
            raise Exception("未连接到服务器")
        
        message = await self.websocket.recv()
        return json.loads(message)
    
    async def synthesize_speech(self, text: str, text_lang: str = "zh", 
                              ref_audio_path: str = "", prompt_text: str = "",
                              prompt_lang: str = "zh", streaming: bool = True,
                              output_file: str = None):
        """合成语音"""
        if not self.websocket:
            raise Exception("未连接到服务器")
        
        # 发送TTS请求
        request = {
            "type": "tts_request",
            "data": {
                "text": text,
                "text_lang": text_lang,
                "ref_audio_path": ref_audio_path,
                "prompt_text": prompt_text,
                "prompt_lang": prompt_lang,
                "streaming": streaming,
                "media_type": "wav"
            }
        }
        
        await self.send_message(request)
        logger.info(f"开始合成语音: {text[:50]}...")
        
        # 重置音频缓冲区
        self.audio_chunks = []
        
        # 接收响应
        while True:
            try:
                message = await self.receive_message()
                
                if message["type"] == "tts_started":
                    logger.info("TTS合成开始")
                
                elif message["type"] == "audio_chunk":
                    data = message["data"]
                    audio_data = data["audio"]
                    
                    if audio_data:  # 非空音频数据
                        self.audio_chunks.append(audio_data)
                        logger.info(f"收到音频片段 {data.get('chunk_index', '?')}")
                    
                    if data["is_final"]:
                        logger.info("音频合成完成")
                        break
                
                elif message["type"] == "error":
                    logger.error(f"服务器错误: {message['data']['message']}")
                    return None
                
                elif message["type"] == "connection_established":
                    logger.info(f"连接已建立: {message['data']['connection_id']}")
                    logger.info(f"服务器版本: {message['data']['server_info']['version']}")
                
            except websockets.exceptions.ConnectionClosed:
                logger.error("连接已断开")
                break
            except Exception as e:
                logger.error(f"接收消息时出错: {e}")
                break
        
        # 合并音频数据
        if self.audio_chunks:
            combined_audio = self.combine_audio_chunks()
            
            # 保存音频文件
            if output_file:
                self.save_audio(combined_audio, output_file)
                logger.info(f"音频已保存到: {output_file}")
            
            return combined_audio
        
        return None
    
    def combine_audio_chunks(self):
        """合并音频片段"""
        if not self.audio_chunks:
            return None
        
        # 将所有base64音频数据合并
        combined_base64 = "".join(self.audio_chunks)
        
        # 解码base64数据
        audio_bytes = base64.b64decode(combined_base64)
        
        return audio_bytes
    
    def save_audio(self, audio_bytes: bytes, output_file: str):
        """保存音频到文件"""
        with open(output_file, "wb") as f:
            f.write(audio_bytes)
    
    async def ping(self):
        """发送ping消息"""
        import time
        ping_message = {
            "type": "ping",
            "data": {"timestamp": time.time()}
        }
        await self.send_message(ping_message)
        
        # 等待pong响应
        message = await self.receive_message()
        if message["type"] == "pong":
            logger.info("Ping成功")
            return True
        return False


async def main():
    parser = argparse.ArgumentParser(description="WebSocket TTS 客户端")
    parser.add_argument("--url", default="ws://localhost:9881/ws", help="WebSocket服务器地址")
    parser.add_argument("--text", default="你好，这是WebSocket TTS测试。", help="要合成的文本")
    parser.add_argument("--text-lang", default="zh", help="文本语言")
    parser.add_argument("--ref-audio", default="", help="参考音频路径")
    parser.add_argument("--prompt-text", default="", help="参考音频文本")
    parser.add_argument("--prompt-lang", default="zh", help="参考音频语言")
    parser.add_argument("--output", default="output.wav", help="输出音频文件")
    parser.add_argument("--no-streaming", action="store_true", help="禁用流式模式")
    
    args = parser.parse_args()
    
    # 创建客户端
    client = WebSocketTTSClient(args.url)
    
    try:
        # 连接到服务器
        if not await client.connect():
            return
        
        # 测试ping
        await client.ping()
        
        # 合成语音
        audio_data = await client.synthesize_speech(
            text=args.text,
            text_lang=args.text_lang,
            ref_audio_path=args.ref_audio,
            prompt_text=args.prompt_text,
            prompt_lang=args.prompt_lang,
            streaming=not args.no_streaming,
            output_file=args.output
        )
        
        if audio_data:
            logger.info("语音合成成功！")
        else:
            logger.error("语音合成失败")
    
    except KeyboardInterrupt:
        logger.info("用户中断")
    except Exception as e:
        logger.error(f"客户端错误: {e}")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
