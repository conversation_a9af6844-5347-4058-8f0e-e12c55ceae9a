/**
 * WebSocket TTS 客户端 (JavaScript/Node.js)
 * 演示如何使用JavaScript连接WebSocket TTS服务
 */

const WebSocket = require('ws');
const fs = require('fs');

class WebSocketTTSClient {
    constructor(url) {
        this.url = url;
        this.ws = null;
        this.audioChunks = [];
        this.messageHandlers = {};
    }

    connect() {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(this.url);
                
                this.ws.on('open', () => {
                    console.log(`✓ 已连接到: ${this.url}`);
                    resolve();
                });
                
                this.ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data.toString());
                        this.handleMessage(message);
                    } catch (error) {
                        console.error('解析消息失败:', error);
                    }
                });
                
                this.ws.on('close', () => {
                    console.log('✓ 连接已断开');
                });
                
                this.ws.on('error', (error) => {
                    console.error('WebSocket错误:', error);
                    reject(error);
                });
                
            } catch (error) {
                reject(error);
            }
        });
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

    sendMessage(message) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            throw new Error('WebSocket未连接');
        }
        
        this.ws.send(JSON.stringify(message));
        console.log(`📤 发送消息: ${message.type}`);
    }

    handleMessage(message) {
        console.log(`📥 收到消息: ${message.type}`);
        
        // 调用注册的处理器
        if (this.messageHandlers[message.type]) {
            this.messageHandlers[message.type](message);
        }
        
        // 默认处理
        switch (message.type) {
            case 'connection_established':
                console.log(`连接ID: ${message.data.connection_id}`);
                console.log(`服务器版本: ${message.data.server_info.version}`);
                console.log(`设备: ${message.data.server_info.device}`);
                break;
                
            case 'tts_started':
                console.log('🎵 TTS合成开始');
                this.audioChunks = [];
                break;
                
            case 'audio_chunk':
                const audioData = message.data.audio;
                if (audioData) {
                    this.audioChunks.push(audioData);
                    console.log(`🎵 收到音频片段 ${message.data.chunk_index || '?'}`);
                }
                
                if (message.data.is_final) {
                    console.log('✅ 音频合成完成');
                    this.onAudioComplete();
                }
                break;
                
            case 'error':
                console.error(`❌ 服务器错误: ${message.data.message}`);
                break;
                
            case 'pong':
                console.log('🏓 Pong收到');
                break;
        }
    }

    onMessage(type, handler) {
        this.messageHandlers[type] = handler;
    }

    onAudioComplete() {
        // 子类可以重写此方法
        console.log(`🎵 音频合成完成，共 ${this.audioChunks.length} 个片段`);
    }

    async synthesizeSpeech(options) {
        const {
            text,
            textLang = 'zh',
            refAudioPath = '',
            promptText = '',
            promptLang = 'zh',
            streaming = true,
            mediaType = 'wav'
        } = options;

        return new Promise((resolve, reject) => {
            // 设置完成处理器
            this.onMessage('audio_chunk', (message) => {
                if (message.data.is_final) {
                    resolve(this.audioChunks);
                }
            });

            // 设置错误处理器
            this.onMessage('error', (message) => {
                reject(new Error(message.data.message));
            });

            // 发送TTS请求
            const request = {
                type: 'tts_request',
                data: {
                    text,
                    text_lang: textLang,
                    ref_audio_path: refAudioPath,
                    prompt_text: promptText,
                    prompt_lang: promptLang,
                    streaming,
                    media_type: mediaType
                }
            };

            this.sendMessage(request);
            console.log(`🎵 开始合成语音: ${text.substring(0, 50)}...`);
        });
    }

    ping() {
        return new Promise((resolve) => {
            this.onMessage('pong', () => {
                resolve(true);
            });

            this.sendMessage({
                type: 'ping',
                data: { timestamp: Date.now() }
            });
        });
    }

    saveAudio(audioChunks, outputFile) {
        if (!audioChunks || audioChunks.length === 0) {
            throw new Error('没有音频数据');
        }

        // 合并所有音频片段
        const combinedBase64 = audioChunks.join('');
        
        // 解码base64数据
        const audioBuffer = Buffer.from(combinedBase64, 'base64');
        
        // 保存到文件
        fs.writeFileSync(outputFile, audioBuffer);
        console.log(`💾 音频已保存到: ${outputFile}`);
    }
}

// 命令行使用示例
async function main() {
    const args = process.argv.slice(2);
    const options = {};
    
    // 简单的命令行参数解析
    for (let i = 0; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        options[key] = value;
    }
    
    const {
        url = 'ws://localhost:9881/ws',
        text = '你好，这是WebSocket TTS测试。',
        'text-lang': textLang = 'zh',
        'ref-audio': refAudioPath = '',
        'prompt-text': promptText = '',
        'prompt-lang': promptLang = 'zh',
        output = 'output.wav'
    } = options;

    const client = new WebSocketTTSClient(url);

    try {
        // 连接到服务器
        await client.connect();
        
        // 测试ping
        console.log('🏓 测试连接...');
        await client.ping();
        
        // 合成语音
        const audioChunks = await client.synthesizeSpeech({
            text,
            textLang,
            refAudioPath,
            promptText,
            promptLang,
            streaming: true
        });
        
        // 保存音频
        client.saveAudio(audioChunks, output);
        console.log('✅ 语音合成成功！');
        
    } catch (error) {
        console.error('❌ 客户端错误:', error.message);
    } finally {
        client.disconnect();
    }
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = WebSocketTTSClient;
