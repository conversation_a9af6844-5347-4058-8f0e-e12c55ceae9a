# WebSocket TTS 服务

基于 GPT-SoVITS 的原生 WebSocket 实时语音合成服务，支持流式音频传输和实时语音合成。

## 🚀 快速开始

### 1. 安装依赖

确保已安装 GPT-SoVITS 的所有依赖：

```bash
pip install -r requirements.txt
```

### 2. 启动服务

#### 方式一：使用启动脚本（推荐）

```bash
python start_websocket_tts.py
```

#### 方式二：直接启动

```bash
python websocket_tts.py -c GPT_SoVITS/configs/tts_infer.yaml -p 9881
```

### 3. 测试服务

打开浏览器访问：`http://localhost:9881`

## 📡 API 接口

### WebSocket 连接

- **地址**: `ws://localhost:9881/ws`
- **协议**: WebSocket
- **数据格式**: JSON

### 消息类型

#### 1. TTS 请求

```json
{
    "type": "tts_request",
    "data": {
        "text": "要合成的文本",
        "text_lang": "zh",
        "ref_audio_path": "参考音频路径",
        "prompt_text": "参考音频文本",
        "prompt_lang": "zh",
        "streaming": true,
        "media_type": "wav",
        "top_k": 5,
        "top_p": 1.0,
        "temperature": 1.0,
        "speed_factor": 1.0
    }
}
```

#### 2. 停止合成

```json
{
    "type": "stop_tts",
    "data": {}
}
```

#### 3. 心跳检测

```json
{
    "type": "ping",
    "data": {"timestamp": 1234567890}
}
```

### 响应消息

#### 1. 连接建立

```json
{
    "type": "connection_established",
    "data": {
        "connection_id": "uuid",
        "server_info": {
            "version": "v2",
            "device": "cuda",
            "supported_languages": ["zh", "en", "ja", "ko", "yue"]
        }
    }
}
```

#### 2. 合成开始

```json
{
    "type": "tts_started",
    "data": {
        "text": "要合成的文本",
        "text_lang": "zh",
        "streaming": true
    }
}
```

#### 3. 音频片段

```json
{
    "type": "audio_chunk",
    "data": {
        "audio": "base64编码的音频数据",
        "sample_rate": 24000,
        "chunk_index": 1,
        "is_final": false,
        "media_type": "wav"
    }
}
```

#### 4. 错误消息

```json
{
    "type": "error",
    "data": {
        "message": "错误描述"
    }
}
```

## 🛠️ 客户端示例

### Python 客户端

```bash
python websocket_tts_client.py --text "你好世界" --output output.wav
```

### JavaScript/Node.js 客户端

```bash
node websocket_tts_client.js --text "你好世界" --output output.wav
```

### 浏览器 JavaScript

```javascript
const ws = new WebSocket('ws://localhost:9881/ws');

ws.onopen = function() {
    // 发送TTS请求
    ws.send(JSON.stringify({
        type: 'tts_request',
        data: {
            text: '你好，这是测试文本',
            text_lang: 'zh',
            streaming: true,
            media_type: 'wav'
        }
    }));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    
    if (message.type === 'audio_chunk') {
        const audioData = message.data.audio;
        // 处理音频数据...
        
        if (message.data.is_final) {
            console.log('音频合成完成');
        }
    }
};
```

## ⚙️ 配置参数

### 服务器参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `-c, --config` | `GPT_SoVITS/configs/tts_infer.yaml` | TTS配置文件路径 |
| `-a, --host` | `0.0.0.0` | 绑定地址 |
| `-p, --port` | `9881` | 服务端口 |

### TTS 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `text` | string | 必需 | 要合成的文本 |
| `text_lang` | string | `"zh"` | 文本语言 |
| `ref_audio_path` | string | `""` | 参考音频路径 |
| `prompt_text` | string | `""` | 参考音频文本 |
| `prompt_lang` | string | `"zh"` | 参考音频语言 |
| `streaming` | boolean | `true` | 是否启用流式传输 |
| `media_type` | string | `"wav"` | 音频格式 |
| `top_k` | integer | `5` | Top-K 采样 |
| `top_p` | float | `1.0` | Top-P 采样 |
| `temperature` | float | `1.0` | 采样温度 |
| `speed_factor` | float | `1.0` | 语速控制 |

### 支持的语言

- `zh`: 中文
- `en`: 英文  
- `ja`: 日文
- `ko`: 韩文
- `yue`: 粤语

### 支持的音频格式

- `wav`: WAV 格式（推荐）
- `ogg`: OGG 格式
- `flac`: FLAC 格式

## 🔧 高级功能

### 1. 流式音频传输

启用 `streaming: true` 可以实现实时音频流传输，音频会分片段逐步返回。

### 2. 参考音频克隆

通过设置 `ref_audio_path` 和 `prompt_text` 可以实现声音克隆。

### 3. 多语言支持

支持中文、英文、日文、韩文、粤语的语音合成。

### 4. 语速控制

通过 `speed_factor` 参数控制语音播放速度（0.5-2.0）。

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查服务是否正常启动
   - 确认端口未被占用
   - 检查防火墙设置

2. **音频质量差**
   - 检查参考音频质量
   - 调整采样参数
   - 确保模型文件完整

3. **合成速度慢**
   - 检查GPU是否可用
   - 调整batch_size参数
   - 启用并行推理

### 日志查看

服务运行时会输出详细日志，可以通过日志排查问题：

```bash
python websocket_tts.py -c config.yaml 2>&1 | tee websocket_tts.log
```

## 📈 性能优化

1. **使用GPU加速**: 确保CUDA环境正确配置
2. **调整批处理大小**: 根据显存大小调整batch_size
3. **启用并行推理**: 设置 `parallel_infer: true`
4. **优化网络传输**: 使用适当的音频压缩格式

## 🔒 安全注意事项

1. **生产环境部署**时应该：
   - 限制允许的来源域名
   - 设置适当的速率限制
   - 使用HTTPS/WSS加密传输
   - 限制文本长度和请求频率

2. **文件路径安全**：
   - 验证参考音频文件路径
   - 防止路径遍历攻击

## 📄 许可证

本项目基于 MIT 许可证开源。
