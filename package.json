{"name": "gpt-sovits-websocket-tts", "version": "1.0.0", "description": "WebSocket TTS client for GPT-SoVITS", "main": "websocket_tts_client.js", "scripts": {"start": "node websocket_tts_client.js", "test": "node websocket_tts_client.js --text '这是一个测试' --output test_output.wav"}, "keywords": ["tts", "websocket", "gpt-sovits", "speech-synthesis", "voice-cloning"], "author": "GPT-SoVITS WebSocket TTS", "license": "MIT", "dependencies": {"ws": "^8.14.2"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/RVC-Boss/GPT-SoVITS"}, "bugs": {"url": "https://github.com/RVC-Boss/GPT-SoVITS/issues"}, "homepage": "https://github.com/RVC-Boss/GPT-SoVITS#readme"}