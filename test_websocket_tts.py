#!/usr/bin/env python3
"""
WebSocket TTS 服务测试脚本
用于验证WebSocket TTS服务是否正常工作
"""

import asyncio
import json
import logging
import time
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WebSocketTTSTest:
    def __init__(self, url: str = "ws://localhost:9881/ws"):
        self.url = url
        self.websocket = None
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(self.url)
            logger.info(f"✅ 连接成功: {self.url}")
            return True
        except Exception as e:
            logger.error(f"❌ 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            logger.info("✅ 连接已断开")
    
    async def send_message(self, message: dict):
        """发送消息"""
        if not self.websocket:
            raise Exception("未连接到服务器")
        
        await self.websocket.send(json.dumps(message))
        logger.info(f"📤 发送消息: {message['type']}")
    
    async def receive_message(self, timeout: float = 10.0):
        """接收消息"""
        if not self.websocket:
            raise Exception("未连接到服务器")
        
        try:
            message = await asyncio.wait_for(self.websocket.recv(), timeout=timeout)
            return json.loads(message)
        except asyncio.TimeoutError:
            logger.warning(f"⏰ 接收消息超时 ({timeout}s)")
            return None
    
    async def test_connection(self):
        """测试连接"""
        logger.info("🔍 测试1: 连接测试")
        
        if not await self.connect():
            return False
        
        # 等待连接建立消息
        message = await self.receive_message()
        if message and message.get("type") == "connection_established":
            logger.info("✅ 连接建立消息接收成功")
            server_info = message.get("data", {}).get("server_info", {})
            logger.info(f"   服务器版本: {server_info.get('version', 'unknown')}")
            logger.info(f"   设备: {server_info.get('device', 'unknown')}")
            return True
        else:
            logger.error("❌ 未收到连接建立消息")
            return False
    
    async def test_ping(self):
        """测试心跳"""
        logger.info("🔍 测试2: 心跳测试")
        
        # 发送ping
        ping_time = time.time()
        await self.send_message({
            "type": "ping",
            "data": {"timestamp": ping_time}
        })
        
        # 等待pong响应
        message = await self.receive_message()
        if message and message.get("type") == "pong":
            pong_time = time.time()
            latency = (pong_time - ping_time) * 1000
            logger.info(f"✅ 心跳测试成功，延迟: {latency:.2f}ms")
            return True
        else:
            logger.error("❌ 心跳测试失败")
            return False
    
    async def test_tts_simple(self):
        """测试简单TTS"""
        logger.info("🔍 测试3: 简单TTS测试")
        
        # 发送TTS请求
        await self.send_message({
            "type": "tts_request",
            "data": {
                "text": "这是一个WebSocket TTS测试。",
                "text_lang": "zh",
                "streaming": False,  # 非流式模式，更容易测试
                "media_type": "wav"
            }
        })
        
        # 等待TTS开始消息
        message = await self.receive_message()
        if not message or message.get("type") != "tts_started":
            logger.error("❌ 未收到TTS开始消息")
            return False
        
        logger.info("✅ TTS合成开始")
        
        # 等待音频数据
        message = await self.receive_message(timeout=30.0)  # TTS可能需要更长时间
        if not message or message.get("type") != "audio_chunk":
            logger.error("❌ 未收到音频数据")
            return False
        
        audio_data = message.get("data", {}).get("audio", "")
        is_final = message.get("data", {}).get("is_final", False)
        
        if audio_data and is_final:
            logger.info(f"✅ TTS测试成功，音频数据长度: {len(audio_data)} 字符")
            return True
        else:
            logger.error("❌ TTS测试失败，音频数据无效")
            return False
    
    async def test_tts_streaming(self):
        """测试流式TTS"""
        logger.info("🔍 测试4: 流式TTS测试")
        
        # 发送流式TTS请求
        await self.send_message({
            "type": "tts_request",
            "data": {
                "text": "这是一个流式WebSocket TTS测试，用于验证实时音频传输功能。",
                "text_lang": "zh",
                "streaming": True,
                "media_type": "wav"
            }
        })
        
        # 等待TTS开始消息
        message = await self.receive_message()
        if not message or message.get("type") != "tts_started":
            logger.error("❌ 未收到TTS开始消息")
            return False
        
        logger.info("✅ 流式TTS合成开始")
        
        # 接收音频片段
        chunk_count = 0
        while True:
            message = await self.receive_message(timeout=30.0)
            if not message:
                logger.error("❌ 接收音频片段超时")
                return False
            
            if message.get("type") == "audio_chunk":
                chunk_count += 1
                is_final = message.get("data", {}).get("is_final", False)
                audio_data = message.get("data", {}).get("audio", "")
                
                if audio_data:
                    logger.info(f"📦 收到音频片段 {chunk_count}")
                
                if is_final:
                    logger.info(f"✅ 流式TTS测试成功，共收到 {chunk_count} 个片段")
                    return True
            
            elif message.get("type") == "error":
                logger.error(f"❌ 服务器错误: {message.get('data', {}).get('message', 'unknown')}")
                return False
    
    async def test_error_handling(self):
        """测试错误处理"""
        logger.info("🔍 测试5: 错误处理测试")
        
        # 发送无效请求
        await self.send_message({
            "type": "tts_request",
            "data": {
                "text": "",  # 空文本应该触发错误
                "text_lang": "zh"
            }
        })
        
        # 等待错误消息
        message = await self.receive_message()
        if message and message.get("type") == "error":
            logger.info(f"✅ 错误处理测试成功: {message.get('data', {}).get('message', '')}")
            return True
        else:
            logger.error("❌ 错误处理测试失败")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始WebSocket TTS服务测试")
        logger.info("=" * 50)
        
        tests = [
            ("连接测试", self.test_connection),
            ("心跳测试", self.test_ping),
            ("简单TTS测试", self.test_tts_simple),
            ("流式TTS测试", self.test_tts_streaming),
            ("错误处理测试", self.test_error_handling)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if await test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} - 通过")
                else:
                    logger.error(f"❌ {test_name} - 失败")
            except Exception as e:
                logger.error(f"❌ {test_name} - 异常: {e}")
            
            logger.info("-" * 30)
        
        await self.disconnect()
        
        logger.info("=" * 50)
        logger.info(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！WebSocket TTS服务工作正常")
            return True
        else:
            logger.warning("⚠️  部分测试失败，请检查服务配置")
            return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="WebSocket TTS 服务测试")
    parser.add_argument("--url", default="ws://localhost:9881/ws", help="WebSocket服务器地址")
    
    args = parser.parse_args()
    
    tester = WebSocketTTSTest(args.url)
    
    try:
        success = await tester.run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"💥 测试过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
