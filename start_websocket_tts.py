#!/usr/bin/env python3
"""
WebSocket TTS 服务启动脚本
简化的启动方式，自动检测配置和模型
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def find_config_file():
    """查找可用的配置文件"""
    possible_configs = [
        "GPT_SoVITS/configs/tts_infer.yaml",
        "configs/tts_infer.yaml",
        "tts_infer.yaml"
    ]
    
    for config in possible_configs:
        if os.path.exists(config):
            return config
    
    return None

def check_dependencies():
    """检查必要的依赖"""
    try:
        import fastapi
        import uvicorn
        import soundfile
        print("✓ 依赖检查通过")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install fastapi[standard] soundfile")
        return False

def main():
    parser = argparse.ArgumentParser(description="启动WebSocket TTS服务")
    parser.add_argument("-p", "--port", type=int, default=9881, help="端口号 (默认: 9881)")
    parser.add_argument("-a", "--host", type=str, default="0.0.0.0", help="绑定地址 (默认: 0.0.0.0)")
    parser.add_argument("-c", "--config", type=str, help="配置文件路径")
    parser.add_argument("--no-browser", action="store_true", help="不自动打开浏览器")
    
    args = parser.parse_args()
    
    print("🚀 启动WebSocket TTS服务...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 查找配置文件
    config_path = args.config or find_config_file()
    if not config_path:
        print("✗ 未找到配置文件")
        print("请确保存在以下文件之一:")
        print("  - GPT_SoVITS/configs/tts_infer.yaml")
        print("  - configs/tts_infer.yaml")
        print("  - tts_infer.yaml")
        print("或使用 -c 参数指定配置文件路径")
        sys.exit(1)
    
    print(f"✓ 使用配置文件: {config_path}")
    
    # 构建启动命令
    cmd = [
        sys.executable,
        "websocket_tts.py",
        "-c", config_path,
        "-a", args.host,
        "-p", str(args.port)
    ]
    
    print(f"✓ 服务地址: http://{args.host}:{args.port}")
    print(f"✓ WebSocket地址: ws://{args.host}:{args.port}/ws")
    print("✓ 启动服务中...")
    
    try:
        # 启动服务
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n✓ 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"✗ 服务启动失败: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("✗ 未找到 websocket_tts.py 文件")
        print("请确保在正确的目录中运行此脚本")
        sys.exit(1)

if __name__ == "__main__":
    main()
