@echo off
chcp 65001 >nul
title WebSocket TTS 服务器

echo.
echo ========================================
echo    WebSocket TTS 服务器启动脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.10+
    pause
    exit /b 1
)

:: 检查配置文件
set CONFIG_FILE=GPT_SoVITS\configs\tts_infer.yaml
if not exist "%CONFIG_FILE%" (
    echo ❌ 错误: 未找到配置文件 %CONFIG_FILE%
    echo 请确保GPT-SoVITS已正确安装和配置
    pause
    exit /b 1
)

:: 检查主服务文件
if not exist "websocket_tts.py" (
    echo ❌ 错误: 未找到 websocket_tts.py 文件
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo ✓ 环境检查通过
echo ✓ 配置文件: %CONFIG_FILE%
echo.

:: 设置默认参数
set HOST=0.0.0.0
set PORT=9881

:: 显示启动信息
echo 🚀 启动WebSocket TTS服务器...
echo 📡 服务地址: http://%HOST%:%PORT%
echo 🔗 WebSocket地址: ws://%HOST%:%PORT%/ws
echo 🌐 测试页面: http://localhost:%PORT%
echo.
echo 按 Ctrl+C 停止服务器
echo.

:: 启动服务器
python websocket_tts.py -c "%CONFIG_FILE%" -a %HOST% -p %PORT%

:: 如果服务器异常退出
echo.
echo ⚠️  服务器已停止
pause
